# Custom Fields v2 Integration Testing Guide

This guide explains how to test the v2 custom field synchronization system integration with the `/cf` route and real field mappings.

## Overview

The v2 custom field synchronization system has been integrated with the existing `/cf` route and provides comprehensive testing endpoints to validate the Phase 3 implementation.

## Fixed Issues

### 1. TypeScript Errors Resolved

- **Removed all `as any` usage**: Replaced with proper type annotations
- **Fixed FieldDefinitionSync integration**: Modified `synchronizeFieldDefinitions` to accept optional pre-fetched fields
- **Added proper patient data interfaces**: Created `PatientDataWithCustomFields` interface
- **Fixed enum imports**: Properly imported and used `FieldMatchStrategy` and `SyncErrorType`

### 2. Integration with /cf Route

The `/cf` route now uses the v2 system:
- **Re-enabled field synchronization**: Replaced mock response with actual v2 implementation
- **Preserved existing functionality**: Rate limiting and callback handling maintained
- **Real field fetching**: Uses actual AP and CC API calls
- **Proper error handling**: Comprehensive error responses

## Testing Endpoints

### 1. Field Synchronization Test
```
GET /test/cf/sync-fields
```

Tests the complete field definition synchronization process:
- Fetches real custom fields from both AP and CC platforms
- Uses v2 field matching and type compatibility checking
- Creates missing fields (CC → AP only)
- Stores field mappings in database
- Returns detailed sync results

**Example Response:**
```json
{
  "status": "success",
  "message": "Test field synchronization completed",
  "data": {
    "requestId": "test-sync-1691234567890",
    "syncResult": {
      "success": true,
      "totalFields": { "ap": 15, "cc": 12 },
      "matchedFields": 8,
      "createdFields": 3,
      "skippedFields": 1,
      "failedFields": 0,
      "processingTimeMs": 1250
    }
  }
}
```

### 2. Patient Value Synchronization Test
```
POST /test/cf/sync-patient-values
Content-Type: application/json

{
  "patientData": {
    "id": "patient-123",
    "customFields": [
      {
        "id": "field-1",
        "value": "<EMAIL>"
      }
    ]
  },
  "platform": "ap"
}
```

Tests patient custom field value synchronization:
- Uses real field mappings from database
- Converts values between platforms
- Validates type compatibility
- Returns detailed sync summary

**Example Response:**
```json
{
  "status": "success",
  "message": "Test patient value synchronization completed",
  "data": {
    "syncResult": {
      "patientId": "patient-123",
      "platform": "ap",
      "totalFields": 5,
      "processedFields": 4,
      "successfulUpdates": 3,
      "failedUpdates": 1,
      "processingTimeMs": 850
    }
  }
}
```

### 3. Field Mappings Viewer
```
GET /test/cf/field-mappings
```

Retrieves and displays current field mappings in both original and v2 formats:
- Shows existing database mappings
- Converts to v2 format for compatibility
- Provides mapping statistics

## Production /cf Route

The main `/cf` route has been updated to use the v2 system:

```
GET /cf
```

- **Rate limiting**: 4 calls per 24 hours (currently disabled for testing)
- **Real synchronization**: Uses v2 CustomFieldSyncV2 class
- **Field matching**: NORMALIZED strategy with 0.85 fuzzy threshold
- **Field creation**: Creates missing CC fields in AP
- **Callback support**: Maintains existing callback functionality

## Testing Workflow

### 1. Basic Field Synchronization
```bash
# Test field synchronization
curl -X GET "http://localhost:8787/test/cf/sync-fields"

# Check the main /cf route
curl -X GET "http://localhost:8787/cf"
```

### 2. Patient Value Synchronization
```bash
# Test with AP patient data
curl -X POST "http://localhost:8787/test/cf/sync-patient-values" \
  -H "Content-Type: application/json" \
  -d '{
    "patientData": {
      "id": "test-patient-123",
      "customFields": [
        {
          "id": "email-field",
          "value": "<EMAIL>"
        }
      ]
    },
    "platform": "ap"
  }'

# Test with CC patient data
curl -X POST "http://localhost:8787/test/cf/sync-patient-values" \
  -H "Content-Type: application/json" \
  -d '{
    "patientData": {
      "id": "test-patient-456",
      "customFields": [
        {
          "field": {
            "id": 1,
            "name": "email",
            "type": "email"
          },
          "value": "<EMAIL>"
        }
      ]
    },
    "platform": "cc"
  }'
```

### 3. View Field Mappings
```bash
# Get current field mappings
curl -X GET "http://localhost:8787/test/cf/field-mappings"
```

## Validation Checklist

### ✅ TypeScript Compliance
- [ ] No `any` types or `as any` assertions
- [ ] Proper type imports and usage
- [ ] All method signatures use specific types
- [ ] Enum values properly imported and used

### ✅ Integration Functionality
- [ ] `/cf` route uses v2 system
- [ ] Real field fetching from AP and CC APIs
- [ ] Field mappings stored in database
- [ ] Error handling works correctly
- [ ] Rate limiting preserved (when enabled)

### ✅ Test Endpoints
- [ ] Field synchronization test works
- [ ] Patient value synchronization test works
- [ ] Field mappings viewer works
- [ ] All endpoints return proper JSON responses
- [ ] Error cases handled gracefully

### ✅ Real Data Integration
- [ ] Uses actual field mappings from database
- [ ] Works with real AP and CC field structures
- [ ] Proper value conversion between platforms
- [ ] Database operations work correctly

## Configuration

The v2 system uses the following configuration:

```typescript
const customFieldSync = new CustomFieldSyncV2({
  fieldMatchConfig: {
    strategy: FieldMatchStrategy.NORMALIZED,
    fuzzyThreshold: 0.85,
    normalizeGermanChars: true,
    ignoreCase: true,
    ignoreSpaces: true,
  },
});
```

## Error Handling

All endpoints provide comprehensive error responses:

```json
{
  "status": "error",
  "message": "Descriptive error message",
  "data": {
    "requestId": "unique-request-id",
    "timestamp": "2024-08-07T10:30:00.000Z",
    "error": "Detailed error information"
  }
}
```

## Next Steps

1. **Test with real data**: Use actual patient data and field configurations
2. **Performance testing**: Validate with large datasets
3. **Error scenarios**: Test edge cases and error conditions
4. **Production deployment**: Enable rate limiting and deploy to production
5. **Monitoring**: Set up logging and monitoring for the v2 system

## Support

For issues or questions about the v2 integration:
1. Check the logs for detailed error information
2. Use the test endpoints to isolate issues
3. Review the field mappings to ensure proper configuration
4. Validate that both AP and CC APIs are accessible
