/**
 * Custom Field Synchronization v2 Types
 *
 * Clean, comprehensive type definitions for the v2 custom field synchronization system.
 * Provides strong typing for field matching, value conversion, and synchronization operations.
 *
 * @fileoverview v2 Custom field synchronization type definitions
 * @version 2.0.0
 * @since 2024-08-07
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
} from "@/type/APTypes.js";
import type { GetCCCustomField, PostCCCustomField } from "@/type/CCTypes.js";

/**
 * Platform identifier for synchronization operations
 */
export type Platform = "ap" | "cc";

/**
 * AutoPatient field data types
 */
export type APFieldDataType =
	| "TEXT"
	| "LARGE_TEXT"
	| "NUMERICAL"
	| "PHONE"
	| "MONETORY"
	| "CHECKBOX"
	| "SINGLE_OPTIONS"
	| "MULTIPLE_OPTIONS"
	| "DATE"
	| "RADIO"
	| "EMAIL"
	| "TEXTBOX_LIST"
	| "FILE_UPLOAD";

/**
 * CliniCore field types
 */
export type CCFieldType =
	| "text"
	| "textarea"
	| "select"
	| "boolean"
	| "select-or-custom"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "medication"
	| "permanent-diagnoses"
	| "patient-has-recommended";

/**
 * Standard field types that can be mapped to custom fields
 */
export type StandardFieldType =
	| "email"
	| "phone"
	| "patientId"
	| "ccProfileLink";

/**
 * Field matching strategy enumeration
 */
export enum FieldMatchStrategy {
	EXACT = "exact",
	NORMALIZED = "normalized",
	FUZZY = "fuzzy",
}

/**
 * Field matching configuration
 */
export interface FieldMatchConfig {
	strategy: FieldMatchStrategy;
	fuzzyThreshold?: number; // 0-1, default 0.8
	normalizeGermanChars?: boolean; // default true
	ignoreCase?: boolean; // default true
	ignoreSpaces?: boolean; // default true
}

/**
 * Field type compatibility result
 */
export interface TypeCompatibilityResult {
	compatible: boolean;
	reason?: string;
	requiresConversion?: boolean;
	conversionNotes?: string;
}

/**
 * Field matching result with detailed information
 */
export interface FieldMatchResult {
	matched: boolean;
	apField?: APGetCustomFieldType;
	ccField?: GetCCCustomField;
	matchType: FieldMatchStrategy | "none";
	confidence: number; // 0-1
	typeCompatible: boolean;
	reason?: string;
}

/**
 * Value conversion context with comprehensive metadata
 */
export interface ValueConversionContext {
	sourceType: APFieldDataType | CCFieldType;
	targetType: APFieldDataType | CCFieldType;
	sourceValue: unknown;
	targetField?: APGetCustomFieldType | GetCCCustomField;
	sourceField?: APGetCustomFieldType | GetCCCustomField;
	isMultiValue?: boolean;
	preserveFormat?: boolean;
}

/**
 * Value conversion result with detailed feedback
 */
export interface ValueConversionResult {
	success: boolean;
	convertedValue: unknown;
	originalValue: unknown;
	conversionType: "direct" | "formatted" | "transformed" | "failed";
	warnings?: string[];
	error?: string;
	metadata?: Record<string, unknown>;
}

/**
 * Standard field mapping configuration
 */
export interface StandardFieldMapping {
	standardField: StandardFieldType;
	platform: Platform;
	customFieldName: string;
	customFieldType: APFieldDataType | CCFieldType;
	extractionPath: string; // JSONPath or property path
	required?: boolean;
	defaultValue?: unknown;
}

/**
 * Field synchronization operation result
 */
export interface FieldSyncResult {
	success: boolean;
	action: "created" | "matched" | "updated" | "skipped" | "failed";
	apField?: APGetCustomFieldType;
	ccField?: GetCCCustomField;
	mapping?: FieldMapping;
	error?: string;
	warnings?: string[];
	metadata?: Record<string, unknown>;
}

/**
 * Field mapping database record
 */
export interface FieldMapping {
	id?: string;
	apFieldId?: string;
	ccFieldId?: number;
	apFieldName: string;
	ccFieldName: string;
	apFieldType: APFieldDataType;
	ccFieldType: CCFieldType;
	matchStrategy: FieldMatchStrategy;
	confidence: number;
	isStandardField: boolean;
	standardFieldType?: StandardFieldType;
	createdAt?: Date;
	updatedAt?: Date;
	isActive: boolean;
}

/**
 * Value synchronization context for patient data
 */
export interface ValueSyncContext {
	patientId: string;
	platform: Platform;
	fieldMappings: FieldMapping[];
	standardMappings: StandardFieldMapping[];
	requestId: string;
	dryRun?: boolean;
}

/**
 * Value synchronization result for a single field
 */
export interface ValueSyncResult {
	fieldName: string;
	fieldType: APFieldDataType | CCFieldType;
	success: boolean;
	action: "updated" | "created" | "skipped" | "failed";
	originalValue?: unknown;
	convertedValue?: unknown;
	isStandardField: boolean;
	error?: string;
	warnings?: string[];
}

/**
 * Complete synchronization summary
 */
export interface SyncSummary {
	patientId: string;
	platform: Platform;
	requestId: string;
	startTime: Date;
	endTime: Date;
	processingTimeMs: number;
	totalFields: number;
	processedFields: number;
	successfulUpdates: number;
	failedUpdates: number;
	skippedFields: number;
	standardFieldsProcessed: number;
	customFieldsProcessed: number;
	fieldResults: ValueSyncResult[];
	errors: string[];
	warnings: string[];
}

/**
 * Synchronization options
 */
export interface SyncOptions {
	requestId: string;
	dryRun?: boolean;
	skipExisting?: boolean;
	createMissingFields?: boolean;
	includeStandardFields?: boolean;
	logLevel?: "DEBUG" | "INFO" | "WARN" | "ERROR";
	maxRetries?: number;
	timeout?: number;
}

/**
 * Field creation request for AP
 */
export interface APFieldCreationRequest extends APPostCustomfieldType {
	sourceField: GetCCCustomField;
	mappingReason: string;
	confidence: number;
}

/**
 * Field creation request for CC
 */
export interface CCFieldCreationRequest extends PostCCCustomField {
	sourceField: APGetCustomFieldType;
	mappingReason: string;
	confidence: number;
}

/**
 * Field creation result
 */
export interface FieldCreationResult {
	success: boolean;
	field?: APGetCustomFieldType | GetCCCustomField;
	mapping?: FieldMapping;
	error?: string;
	existingFieldConflict?: boolean;
	errorDetails?: Record<string, unknown>;
}

/**
 * TEXTBOX_LIST specific handling configuration
 */
export interface TextboxListConfig {
	separator: string; // Default: ","
	preserveOrder: boolean; // Default: true
	allowEmpty: boolean; // Default: false
	maxItems?: number;
	trimValues: boolean; // Default: true
}

/**
 * Multi-value conversion configuration
 */
export interface MultiValueConfig {
	textSeparator: string; // Default: " | "
	listSeparator: string; // Default: ","
	preserveEmptyValues: boolean; // Default: false
	normalizeValues: boolean; // Default: true
}

/**
 * Error types for better error handling
 */
export enum SyncErrorType {
	FIELD_NOT_FOUND = "FIELD_NOT_FOUND",
	TYPE_INCOMPATIBLE = "TYPE_INCOMPATIBLE",
	VALUE_CONVERSION_FAILED = "VALUE_CONVERSION_FAILED",
	API_ERROR = "API_ERROR",
	VALIDATION_ERROR = "VALIDATION_ERROR",
	TIMEOUT_ERROR = "TIMEOUT_ERROR",
	NETWORK_ERROR = "NETWORK_ERROR",
	PERMISSION_ERROR = "PERMISSION_ERROR",
}

/**
 * Structured error for synchronization operations
 */
export interface SyncError {
	type: SyncErrorType;
	message: string;
	field?: string;
	originalError?: Error;
	metadata?: Record<string, unknown>;
}

/**
 * Standard field extraction result
 */
export interface StandardFieldExtractionResult {
	fieldType: StandardFieldType;
	value: unknown;
	isValid: boolean;
	transformedValue: unknown;
	error?: string;
}

/**
 * Standard field mapping result
 */
export interface StandardFieldMappingResult {
	success: boolean;
	mappings: StandardFieldMapping[];
	extractions: StandardFieldExtractionResult[];
	errors: SyncError[];
	warnings: string[];
}

/**
 * Patient custom field value structure for AP
 */
export interface APPatientCustomFieldValue {
	id: string;
	value: unknown;
	fieldId?: string;
}

/**
 * Patient custom field value structure for CC
 */
export interface CCPatientCustomFieldValue {
	field: {
		id: number;
		name: string;
		type: CCFieldType;
	};
	value: unknown;
}

/**
 * Patient data structure with custom fields
 */
export interface PatientDataWithCustomFields {
	id?: string;
	patientId?: string;
	patient_id?: string;
	localId?: string;
	local_id?: string;
	customFields?: APPatientCustomFieldValue[] | CCPatientCustomFieldValue[];
	[key: string]: unknown;
}
