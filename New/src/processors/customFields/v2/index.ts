/**
 * Custom Field Synchronization v2 - Main Entry Point
 *
 * Clean, modular entry point for the v2 custom field synchronization system.
 * Provides a unified API for field matching, value conversion, and synchronization.
 *
 * @fileoverview v2 Custom field synchronization main entry point
 * @version 2.0.0
 * @since 2024-08-07
 */

// Configuration
export * from "./config/fieldMappings.js";
export * from "./config/standardMappings.js";
// Core components
export { FieldMatcher } from "./core/fieldMatcher.js";
export { StandardFieldMapper } from "./core/standardFieldMapper.js";
export { TypeChecker } from "./core/typeChecker.js";
export { ValueConverter } from "./core/valueConverter.js";
// Sync engines
export { FieldDefinitionSync } from "./sync/fieldDefinitionSync.js";
export { FieldValueSync } from "./sync/fieldValueSync.js";
export { StandardFieldSync } from "./sync/standardFieldSync.js";

// Types
export * from "./types/index.js";

// Re-export commonly used functions for convenience
import { FieldMatcher } from "./core/fieldMatcher.js";
import { StandardFieldMapper } from "./core/standardFieldMapper.js";
import { logError, logInfo, logWarn } from "@/utils/logger.js";
import { TypeChecker } from "./core/typeChecker.js";
import { ValueConverter } from "./core/valueConverter.js";
import { FieldDefinitionSync } from "./sync/fieldDefinitionSync.js";
import { FieldValueSync } from "./sync/fieldValueSync.js";
import { StandardFieldSync } from "./sync/standardFieldSync.js";
import { SyncErrorType } from "./types/index.js";
import type {
	FieldMatchConfig,
	FieldMatchResult,
	PatientDataWithCustomFields,
	StandardFieldMappingResult,
	SyncOptions,
	ValueConversionContext,
	ValueConversionResult,
} from "./types/index.js";

/**
 * Default instances for common usage
 */
const defaultFieldMatcher = new FieldMatcher();
const defaultValueConverter = new ValueConverter();
const defaultStandardFieldMapper = new StandardFieldMapper();
const defaultTypeChecker = new TypeChecker();

/**
 * Convenience function for field matching
 */
export function matchFields(
	apField: import("@/type/APTypes").APGetCustomFieldType,
	ccFields: import("@/type/CCTypes").GetCCCustomField[],
	config?: Partial<FieldMatchConfig>,
): FieldMatchResult {
	const matcher = config ? new FieldMatcher(config) : defaultFieldMatcher;
	return matcher.findBestMatch(apField, ccFields);
}

/**
 * Convenience function for value conversion
 */
export function convertValue(
	context: ValueConversionContext,
): ValueConversionResult {
	return defaultValueConverter.convertValue(context);
}

/**
 * Convenience function for standard field extraction
 */
export function extractStandardFields(
	patientData: Record<string, unknown>,
	sourcePlatform: "ap" | "cc",
) {
	return defaultStandardFieldMapper.extractStandardFields(
		patientData,
		sourcePlatform,
	);
}

/**
 * Convenience function for creating standard field mappings
 */
export function createStandardFieldMappings(
	extractions: ReturnType<typeof extractStandardFields>,
	targetPlatform: "ap" | "cc",
	existingFields: (
		| import("@/type/APTypes").APGetCustomFieldType
		| import("@/type/CCTypes").GetCCCustomField
	)[],
): StandardFieldMappingResult {
	return defaultStandardFieldMapper.createStandardFieldMappings(
		extractions,
		targetPlatform,
		existingFields,
	);
}

/**
 * Convenience function for type compatibility checking
 */
export function checkTypeCompatibility(
	sourceType:
		| import("./types/index.js").APFieldDataType
		| import("./types/index.js").CCFieldType,
	targetType:
		| import("./types/index.js").APFieldDataType
		| import("./types/index.js").CCFieldType,
	sourceAllowMultiple?: boolean,
	targetAllowMultiple?: boolean,
): import("./core/typeChecker.js").DetailedTypeCompatibility {
	return defaultTypeChecker.checkCompatibility(
		sourceType,
		targetType,
		sourceAllowMultiple,
		targetAllowMultiple,
	);
}

/**
 * Main v2 synchronization API
 */
export class CustomFieldSyncV2 {
	private fieldMatcher: FieldMatcher;
	private valueConverter: ValueConverter;
	private standardFieldMapper: StandardFieldMapper;
	private typeChecker: TypeChecker;
	private fieldDefinitionSync: FieldDefinitionSync;
	private fieldValueSync: FieldValueSync;
	private standardFieldSync: StandardFieldSync;

	constructor(config?: {
		fieldMatchConfig?: Partial<FieldMatchConfig>;
		typeCheckConfig?: Partial<import("./core/typeChecker.js").TypeCheckOptions>;
	}) {
		this.fieldMatcher = new FieldMatcher(config?.fieldMatchConfig);
		this.valueConverter = new ValueConverter();
		this.standardFieldMapper = new StandardFieldMapper();
		this.typeChecker = new TypeChecker(config?.typeCheckConfig);
		this.fieldDefinitionSync = new FieldDefinitionSync({
			fieldMatchConfig: config?.fieldMatchConfig,
			typeCheckConfig: config?.typeCheckConfig,
		});
		this.fieldValueSync = new FieldValueSync({
			typeCheckConfig: config?.typeCheckConfig,
		});
		this.standardFieldSync = new StandardFieldSync({
			typeCheckConfig: config?.typeCheckConfig,
		});
	}

	/**
	 * Get field matcher instance
	 */
	public getFieldMatcher(): FieldMatcher {
		return this.fieldMatcher;
	}

	/**
	 * Get value converter instance
	 */
	public getValueConverter(): ValueConverter {
		return this.valueConverter;
	}

	/**
	 * Get standard field mapper instance
	 */
	public getStandardFieldMapper(): StandardFieldMapper {
		return this.standardFieldMapper;
	}

	/**
	 * Get type checker instance
	 */
	public getTypeChecker(): TypeChecker {
		return this.typeChecker;
	}

	/**
	 * Get field definition sync engine
	 */
	public getFieldDefinitionSync(): FieldDefinitionSync {
		return this.fieldDefinitionSync;
	}

	/**
	 * Get field value sync engine
	 */
	public getFieldValueSync(): FieldValueSync {
		return this.fieldValueSync;
	}

	/**
	 * Get standard field sync engine
	 */
	public getStandardFieldSync(): StandardFieldSync {
		return this.standardFieldSync;
	}

	/**
	 * Perform complete field synchronization
	 *
	 * Orchestrates field definition synchronization using pre-fetched fields.
	 * Matches existing fields, creates missing fields (CC → AP only), and stores mappings.
	 *
	 * @param apFields - Pre-fetched AutoPatient custom fields
	 * @param ccFields - Pre-fetched CliniCore custom fields
	 * @param options - Synchronization options
	 * @returns Comprehensive sync results with detailed field information
	 */
	public async synchronizeFields(
		apFields: import("@/type/APTypes").APGetCustomFieldType[],
		ccFields: import("@/type/CCTypes").GetCCCustomField[],
		options: SyncOptions,
	): Promise<{
		success: boolean;
		totalFields: { ap: number; cc: number };
		matchedFields: number;
		createdFields: number;
		skippedFields: number;
		failedFields: number;
		results: import("./types/index.js").FieldSyncResult[];
		errors: import("./types/index.js").SyncError[];
		warnings: string[];
		processingTimeMs: number;
	}> {
		const startTime = Date.now();
		const results: import("./types/index.js").FieldSyncResult[] = [];
		const errors: import("./types/index.js").SyncError[] = [];
		const warnings: string[] = [];

		logInfo("Starting field synchronization with pre-fetched fields", {
			requestId: options.requestId,
			apFieldCount: apFields.length,
			ccFieldCount: ccFields.length,
			options,
		});

		try {
			// Use the FieldDefinitionSync engine with pre-fetched fields
			const fieldDefSync = this.getFieldDefinitionSync();

			// Call the engine's synchronizeFieldDefinitions with pre-fetched fields
			const result = await fieldDefSync.synchronizeFieldDefinitions(options, {
				apFields,
				ccFields,
			});

			return result;
		} catch (error) {
			const syncError: import("./types/index.js").SyncError = {
				type: SyncErrorType.API_ERROR,
				message: `Field synchronization failed: ${error instanceof Error ? error.message : String(error)}`,
				originalError: error instanceof Error ? error : undefined,
			};

			errors.push(syncError);

			logError("Field synchronization failed", {
				requestId: options.requestId,
				error: syncError.message,
				processingTimeMs: Date.now() - startTime,
			});

			return {
				success: false,
				totalFields: { ap: apFields.length, cc: ccFields.length },
				matchedFields: 0,
				createdFields: 0,
				skippedFields: 0,
				failedFields: 0,
				results,
				errors,
				warnings,
				processingTimeMs: Date.now() - startTime,
			};
		}
	}

	/**
	 * Synchronize patient field values
	 *
	 * Orchestrates patient custom field value synchronization using provided data and mappings.
	 * Converts values between platforms and updates the target platform.
	 *
	 * @param patientData - Patient data containing custom field values
	 * @param fieldMappings - Pre-fetched field mappings between platforms
	 * @param options - Synchronization options
	 * @returns Comprehensive sync summary with detailed results
	 */
	public async synchronizePatientValues(
		patientData: PatientDataWithCustomFields,
		fieldMappings: import("./types/index.js").FieldMapping[],
		options: SyncOptions,
	): Promise<import("./types/index.js").SyncSummary> {
		logInfo("Starting patient value synchronization with provided data", {
			requestId: options.requestId,
			patientDataKeys: Object.keys(patientData),
			fieldMappingCount: fieldMappings.length,
			options,
		});

		try {
			// Extract patient ID from the provided data
			const patientId = this.extractPatientId(patientData);
			if (!patientId) {
				throw new Error("Could not extract patient ID from provided patient data");
			}

			// Determine platform from context - we'll need to infer this from the data structure
			// or require it to be specified in options
			const platform = this.determinePlatform(patientData, options);

			// Construct ValueSyncContext for the FieldValueSync engine
			const syncContext: import("./types/index.js").ValueSyncContext = {
				patientId,
				platform,
				fieldMappings,
				standardMappings: [], // Will be populated if needed
				requestId: options.requestId,
				dryRun: options.dryRun,
			};

			// Use the FieldValueSync engine to perform the actual synchronization
			const fieldValueSync = this.getFieldValueSync();
			const result = await fieldValueSync.synchronizePatientValues(syncContext);

			logInfo("Patient value synchronization completed", {
				requestId: options.requestId,
				patientId,
				platform,
				processingTimeMs: result.processingTimeMs,
				successfulUpdates: result.successfulUpdates,
				failedUpdates: result.failedUpdates,
			});

			return result;
		} catch (error) {
			logError("Patient value synchronization failed", {
				requestId: options.requestId,
				error: String(error),
			});

			// Return a failed SyncSummary
			const endTime = new Date();
			return {
				patientId: this.extractPatientId(patientData) || "unknown",
				platform: "ap", // Default fallback
				requestId: options.requestId,
				startTime: endTime, // Use same time for failed case
				endTime,
				processingTimeMs: 0,
				totalFields: 0,
				processedFields: 0,
				successfulUpdates: 0,
				failedUpdates: 0,
				skippedFields: 0,
				standardFieldsProcessed: 0,
				customFieldsProcessed: 0,
				fieldResults: [],
				errors: [error instanceof Error ? error.message : String(error)],
				warnings: [],
			};
		}
	}

	/**
	 * Extract patient ID from patient data
	 *
	 * @param patientData - Patient data object
	 * @returns Patient ID or null if not found
	 */
	private extractPatientId(patientData: PatientDataWithCustomFields): string | null {
		// Try common patient ID field names
		const idFields = ['id', 'patientId', 'patient_id', 'localId', 'local_id'];

		for (const field of idFields) {
			const value = patientData[field];
			if (value && (typeof value === 'string' || typeof value === 'number')) {
				return String(value);
			}
		}

		logError("Could not extract patient ID from patient data", {
			availableFields: Object.keys(patientData),
			triedFields: idFields,
		});

		return null;
	}

	/**
	 * Determine platform from patient data structure and options
	 *
	 * @param patientData - Patient data object
	 * @param options - Sync options that might contain platform info
	 * @returns Platform identifier
	 */
	private determinePlatform(
		patientData: PatientDataWithCustomFields,
		options: SyncOptions
	): import("./types/index.js").Platform {
		// Check if platform is specified in options (extended interface)
		const extendedOptions = options as SyncOptions & { platform?: import("./types/index.js").Platform };
		if (extendedOptions.platform) {
			return extendedOptions.platform;
		}

		// Try to infer platform from data structure
		// AP typically has different field structures than CC
		if (patientData.customFields && Array.isArray(patientData.customFields)) {
			const customFields = patientData.customFields;
			if (customFields.length > 0) {
				const firstField = customFields[0];
				// CC fields typically have 'field' property with nested structure
				if ('field' in firstField && typeof firstField.field === 'object') {
					return "cc";
				}
				// AP fields typically have direct properties
				if ('id' in firstField && 'value' in firstField) {
					return "ap";
				}
			}
		}

		// Default to AP if we can't determine
		logWarn("Could not determine platform from patient data, defaulting to AP", {
			availableFields: Object.keys(patientData),
		});

		return "ap";
	}
}

/**
 * Create a new v2 synchronization instance
 */
export function createCustomFieldSyncV2(config?: {
	fieldMatchConfig?: Partial<FieldMatchConfig>;
}): CustomFieldSyncV2 {
	return new CustomFieldSyncV2(config);
}

/**
 * Version information
 */
export const VERSION = "2.0.0";
export const BUILD_DATE = "2024-08-07";

/**
 * Feature flags for gradual rollout
 */
export const FEATURE_FLAGS = {
	ENABLE_V2_FIELD_MATCHING: process.env.ENABLE_V2_FIELD_MATCHING === "true",
	ENABLE_V2_VALUE_CONVERSION: process.env.ENABLE_V2_VALUE_CONVERSION === "true",
	ENABLE_V2_STANDARD_FIELDS: process.env.ENABLE_V2_STANDARD_FIELDS === "true",
	ENABLE_V2_FULL_SYNC: process.env.ENABLE_V2_FULL_SYNC === "true",
} as const;

/**
 * Migration helper to check if v2 should be used
 */
export function shouldUseV2(): boolean {
	return process.env.USE_V2_CUSTOM_FIELDS === "true";
}

/**
 * Migration helper for gradual feature rollout
 */
export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
	return FEATURE_FLAGS[feature];
}
